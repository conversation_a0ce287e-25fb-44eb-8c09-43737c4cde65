// Mocks generated by Mockito 5.4.6 from annotations
// in nextsportz_v2/test/features/teams/integration/teams_pagination_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:fpdart/fpdart.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;
import 'package:nextsportz_v2/core/models/paginated_response.dart' as _i8;
import 'package:nextsportz_v2/core/networking/app_error.dart' as _i5;
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart' as _i6;
import 'package:nextsportz_v2/features/teams/domain/repositories/teams_repository.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [TeamsRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockTeamsRepository extends _i1.Mock implements _i2.TeamsRepository {
  MockTeamsRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<_i4.Either<_i5.AppError, List<_i6.TeamSearchItem>>> getMyTeams() =>
      (super.noSuchMethod(
            Invocation.method(#getMyTeams, []),
            returnValue: _i3.Future<
              _i4.Either<_i5.AppError, List<_i6.TeamSearchItem>>
            >.value(
              _i7.dummyValue<
                _i4.Either<_i5.AppError, List<_i6.TeamSearchItem>>
              >(this, Invocation.method(#getMyTeams, [])),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, List<_i6.TeamSearchItem>>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, _i6.Team>> getTeamById(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamById, [teamId]),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, _i6.Team>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, _i6.Team>>(
                this,
                Invocation.method(#getTeamById, [teamId]),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, _i6.Team>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, _i6.Team>> createTeam({
    required String? name,
    required String? description,
    String? logo,
    String? slogan,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createTeam, [], {
              #name: name,
              #description: description,
              #logo: logo,
              #slogan: slogan,
            }),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, _i6.Team>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, _i6.Team>>(
                this,
                Invocation.method(#createTeam, [], {
                  #name: name,
                  #description: description,
                  #logo: logo,
                  #slogan: slogan,
                }),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, _i6.Team>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, _i6.Team>> updateTeam({
    required String? teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateTeam, [], {
              #teamId: teamId,
              #name: name,
              #description: description,
              #logo: logo,
              #slogan: slogan,
            }),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, _i6.Team>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, _i6.Team>>(
                this,
                Invocation.method(#updateTeam, [], {
                  #teamId: teamId,
                  #name: name,
                  #description: description,
                  #logo: logo,
                  #slogan: slogan,
                }),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, _i6.Team>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> deleteTeam(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteTeam, [teamId]),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#deleteTeam, [teamId]),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> invitePlayer({
    required String? teamId,
    required String? playerId,
    String? message,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#invitePlayer, [], {
              #teamId: teamId,
              #playerId: playerId,
              #message: message,
            }),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#invitePlayer, [], {
                  #teamId: teamId,
                  #playerId: playerId,
                  #message: message,
                }),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> acceptInvitation(
    String? invitationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#acceptInvitation, [invitationId]),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#acceptInvitation, [invitationId]),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> declineInvitation(
    String? invitationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#declineInvitation, [invitationId]),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#declineInvitation, [invitationId]),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> removeMember({
    required String? teamId,
    required String? memberId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#removeMember, [], {
              #teamId: teamId,
              #memberId: memberId,
            }),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#removeMember, [], {
                  #teamId: teamId,
                  #memberId: memberId,
                }),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, void>> updateMemberRole({
    required String? teamId,
    required String? memberId,
    required String? role,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateMemberRole, [], {
              #teamId: teamId,
              #memberId: memberId,
              #role: role,
            }),
            returnValue: _i3.Future<_i4.Either<_i5.AppError, void>>.value(
              _i7.dummyValue<_i4.Either<_i5.AppError, void>>(
                this,
                Invocation.method(#updateMemberRole, [], {
                  #teamId: teamId,
                  #memberId: memberId,
                  #role: role,
                }),
              ),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, void>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, List<_i6.TeamInvitation>>>
  getPendingInvitations() =>
      (super.noSuchMethod(
            Invocation.method(#getPendingInvitations, []),
            returnValue: _i3.Future<
              _i4.Either<_i5.AppError, List<_i6.TeamInvitation>>
            >.value(
              _i7.dummyValue<
                _i4.Either<_i5.AppError, List<_i6.TeamInvitation>>
              >(this, Invocation.method(#getPendingInvitations, [])),
            ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, List<_i6.TeamInvitation>>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, List<_i6.Team>>> getTeamsLeaderboard({
    int? limit,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamsLeaderboard, [], {#limit: limit}),
            returnValue:
                _i3.Future<_i4.Either<_i5.AppError, List<_i6.Team>>>.value(
                  _i7.dummyValue<_i4.Either<_i5.AppError, List<_i6.Team>>>(
                    this,
                    Invocation.method(#getTeamsLeaderboard, [], {
                      #limit: limit,
                    }),
                  ),
                ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, List<_i6.Team>>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, List<_i6.Team>>> searchTeams({
    required String? query,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#searchTeams, [], {
              #query: query,
              #limit: limit,
              #offset: offset,
            }),
            returnValue:
                _i3.Future<_i4.Either<_i5.AppError, List<_i6.Team>>>.value(
                  _i7.dummyValue<_i4.Either<_i5.AppError, List<_i6.Team>>>(
                    this,
                    Invocation.method(#searchTeams, [], {
                      #query: query,
                      #limit: limit,
                      #offset: offset,
                    }),
                  ),
                ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, List<_i6.Team>>>);

  @override
  _i3.Future<_i4.Either<_i5.AppError, List<_i6.Team>>> getAllTeams({
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAllTeams, [], {
              #limit: limit,
              #offset: offset,
            }),
            returnValue:
                _i3.Future<_i4.Either<_i5.AppError, List<_i6.Team>>>.value(
                  _i7.dummyValue<_i4.Either<_i5.AppError, List<_i6.Team>>>(
                    this,
                    Invocation.method(#getAllTeams, [], {
                      #limit: limit,
                      #offset: offset,
                    }),
                  ),
                ),
          )
          as _i3.Future<_i4.Either<_i5.AppError, List<_i6.Team>>>);

  @override
  _i3.Future<
    _i4.Either<_i5.AppError, _i8.PaginatedResponse<_i6.TeamSearchItem>>
  >
  searchTeamsPaginated({String? query, int? page, int? pageSize}) =>
      (super.noSuchMethod(
            Invocation.method(#searchTeamsPaginated, [], {
              #query: query,
              #page: page,
              #pageSize: pageSize,
            }),
            returnValue: _i3.Future<
              _i4.Either<
                _i5.AppError,
                _i8.PaginatedResponse<_i6.TeamSearchItem>
              >
            >.value(
              _i7.dummyValue<
                _i4.Either<
                  _i5.AppError,
                  _i8.PaginatedResponse<_i6.TeamSearchItem>
                >
              >(
                this,
                Invocation.method(#searchTeamsPaginated, [], {
                  #query: query,
                  #page: page,
                  #pageSize: pageSize,
                }),
              ),
            ),
          )
          as _i3.Future<
            _i4.Either<_i5.AppError, _i8.PaginatedResponse<_i6.TeamSearchItem>>
          >);

  @override
  _i3.Future<
    _i4.Either<_i5.AppError, _i8.PaginatedResponse<_i6.TeamSearchItem>>
  >
  getAllTeamsPaginated({int? page, int? pageSize}) =>
      (super.noSuchMethod(
            Invocation.method(#getAllTeamsPaginated, [], {
              #page: page,
              #pageSize: pageSize,
            }),
            returnValue: _i3.Future<
              _i4.Either<
                _i5.AppError,
                _i8.PaginatedResponse<_i6.TeamSearchItem>
              >
            >.value(
              _i7.dummyValue<
                _i4.Either<
                  _i5.AppError,
                  _i8.PaginatedResponse<_i6.TeamSearchItem>
                >
              >(
                this,
                Invocation.method(#getAllTeamsPaginated, [], {
                  #page: page,
                  #pageSize: pageSize,
                }),
              ),
            ),
          )
          as _i3.Future<
            _i4.Either<_i5.AppError, _i8.PaginatedResponse<_i6.TeamSearchItem>>
          >);
}
