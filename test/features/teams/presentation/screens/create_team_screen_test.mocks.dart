// Mocks generated by <PERSON>cki<PERSON> 5.4.6 from annotations
// in nextsportz_v2/test/features/teams/presentation/screens/create_team_screen_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i7;

import 'package:fpdart/fpdart.dart' as _i8;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i10;
import 'package:nextsportz_v2/core/models/paginated_response.dart' as _i5;
import 'package:nextsportz_v2/core/networking/api_client.dart' as _i3;
import 'package:nextsportz_v2/core/networking/app_error.dart' as _i9;
import 'package:nextsportz_v2/features/teams/data/datasources/teams_remote_datasource.dart'
    as _i11;
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart' as _i4;
import 'package:nextsportz_v2/features/teams/domain/repositories/teams_repository.dart'
    as _i2;
import 'package:nextsportz_v2/features/teams/domain/usecases/teams_usecases.dart'
    as _i6;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTeamsRepository_0 extends _i1.SmartFake
    implements _i2.TeamsRepository {
  _FakeTeamsRepository_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeApiClient_1 extends _i1.SmartFake implements _i3.ApiClient {
  _FakeApiClient_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeTeam_2 extends _i1.SmartFake implements _i4.Team {
  _FakeTeam_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePaginatedResponse_3<T> extends _i1.SmartFake
    implements _i5.PaginatedResponse<T> {
  _FakePaginatedResponse_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [CreateTeamUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockCreateTeamUseCase extends _i1.Mock implements _i6.CreateTeamUseCase {
  MockCreateTeamUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.TeamsRepository get repository =>
      (super.noSuchMethod(
            Invocation.getter(#repository),
            returnValue: _FakeTeamsRepository_0(
              this,
              Invocation.getter(#repository),
            ),
          )
          as _i2.TeamsRepository);

  @override
  _i7.Future<_i8.Either<_i9.AppError, _i4.Team>> call({
    required String? name,
    required String? description,
    String? logo,
    String? slogan,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#call, [], {
              #name: name,
              #description: description,
              #logo: logo,
              #slogan: slogan,
            }),
            returnValue: _i7.Future<_i8.Either<_i9.AppError, _i4.Team>>.value(
              _i10.dummyValue<_i8.Either<_i9.AppError, _i4.Team>>(
                this,
                Invocation.method(#call, [], {
                  #name: name,
                  #description: description,
                  #logo: logo,
                  #slogan: slogan,
                }),
              ),
            ),
          )
          as _i7.Future<_i8.Either<_i9.AppError, _i4.Team>>);
}

/// A class which mocks [TeamsRemoteDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockTeamsRemoteDataSource extends _i1.Mock
    implements _i11.TeamsRemoteDataSource {
  MockTeamsRemoteDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.ApiClient get apiClient =>
      (super.noSuchMethod(
            Invocation.getter(#apiClient),
            returnValue: _FakeApiClient_1(this, Invocation.getter(#apiClient)),
          )
          as _i3.ApiClient);

  @override
  _i7.Future<List<_i4.TeamSearchItem>> getMyTeams() =>
      (super.noSuchMethod(
            Invocation.method(#getMyTeams, []),
            returnValue: _i7.Future<List<_i4.TeamSearchItem>>.value(
              <_i4.TeamSearchItem>[],
            ),
          )
          as _i7.Future<List<_i4.TeamSearchItem>>);

  @override
  _i7.Future<_i4.Team> getTeamById(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamById, [teamId]),
            returnValue: _i7.Future<_i4.Team>.value(
              _FakeTeam_2(this, Invocation.method(#getTeamById, [teamId])),
            ),
          )
          as _i7.Future<_i4.Team>);

  @override
  _i7.Future<_i4.Team> createTeam({
    required String? name,
    required String? description,
    String? logo,
    String? slogan,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createTeam, [], {
              #name: name,
              #description: description,
              #logo: logo,
              #slogan: slogan,
            }),
            returnValue: _i7.Future<_i4.Team>.value(
              _FakeTeam_2(
                this,
                Invocation.method(#createTeam, [], {
                  #name: name,
                  #description: description,
                  #logo: logo,
                  #slogan: slogan,
                }),
              ),
            ),
          )
          as _i7.Future<_i4.Team>);

  @override
  _i7.Future<_i4.Team> updateTeam({
    required String? teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateTeam, [], {
              #teamId: teamId,
              #name: name,
              #description: description,
              #logo: logo,
              #slogan: slogan,
            }),
            returnValue: _i7.Future<_i4.Team>.value(
              _FakeTeam_2(
                this,
                Invocation.method(#updateTeam, [], {
                  #teamId: teamId,
                  #name: name,
                  #description: description,
                  #logo: logo,
                  #slogan: slogan,
                }),
              ),
            ),
          )
          as _i7.Future<_i4.Team>);

  @override
  _i7.Future<void> deleteTeam(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteTeam, [teamId]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> invitePlayer({
    required String? teamId,
    required String? playerId,
    String? message,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#invitePlayer, [], {
              #teamId: teamId,
              #playerId: playerId,
              #message: message,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> acceptInvitation(String? invitationId) =>
      (super.noSuchMethod(
            Invocation.method(#acceptInvitation, [invitationId]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> declineInvitation(String? invitationId) =>
      (super.noSuchMethod(
            Invocation.method(#declineInvitation, [invitationId]),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> removeMember({
    required String? teamId,
    required String? memberId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#removeMember, [], {
              #teamId: teamId,
              #memberId: memberId,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<void> updateMemberRole({
    required String? teamId,
    required String? memberId,
    required String? role,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateMemberRole, [], {
              #teamId: teamId,
              #memberId: memberId,
              #role: role,
            }),
            returnValue: _i7.Future<void>.value(),
            returnValueForMissingStub: _i7.Future<void>.value(),
          )
          as _i7.Future<void>);

  @override
  _i7.Future<List<_i4.TeamInvitation>> getPendingInvitations() =>
      (super.noSuchMethod(
            Invocation.method(#getPendingInvitations, []),
            returnValue: _i7.Future<List<_i4.TeamInvitation>>.value(
              <_i4.TeamInvitation>[],
            ),
          )
          as _i7.Future<List<_i4.TeamInvitation>>);

  @override
  _i7.Future<String> uploadTeamLogo(dynamic imageFile) =>
      (super.noSuchMethod(
            Invocation.method(#uploadTeamLogo, [imageFile]),
            returnValue: _i7.Future<String>.value(
              _i10.dummyValue<String>(
                this,
                Invocation.method(#uploadTeamLogo, [imageFile]),
              ),
            ),
          )
          as _i7.Future<String>);

  @override
  _i7.Future<List<_i4.Team>> getTeamsLeaderboard({int? limit}) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamsLeaderboard, [], {#limit: limit}),
            returnValue: _i7.Future<List<_i4.Team>>.value(<_i4.Team>[]),
          )
          as _i7.Future<List<_i4.Team>>);

  @override
  _i7.Future<List<_i4.Team>> searchTeams({
    required String? query,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#searchTeams, [], {
              #query: query,
              #limit: limit,
              #offset: offset,
            }),
            returnValue: _i7.Future<List<_i4.Team>>.value(<_i4.Team>[]),
          )
          as _i7.Future<List<_i4.Team>>);

  @override
  _i7.Future<List<_i4.Team>> getAllTeams({int? limit, int? offset}) =>
      (super.noSuchMethod(
            Invocation.method(#getAllTeams, [], {
              #limit: limit,
              #offset: offset,
            }),
            returnValue: _i7.Future<List<_i4.Team>>.value(<_i4.Team>[]),
          )
          as _i7.Future<List<_i4.Team>>);

  @override
  _i7.Future<_i5.PaginatedResponse<_i4.TeamSearchItem>> searchTeamsPaginated({
    String? query,
    int? page,
    int? pageSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#searchTeamsPaginated, [], {
              #query: query,
              #page: page,
              #pageSize: pageSize,
            }),
            returnValue:
                _i7.Future<_i5.PaginatedResponse<_i4.TeamSearchItem>>.value(
                  _FakePaginatedResponse_3<_i4.TeamSearchItem>(
                    this,
                    Invocation.method(#searchTeamsPaginated, [], {
                      #query: query,
                      #page: page,
                      #pageSize: pageSize,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i5.PaginatedResponse<_i4.TeamSearchItem>>);

  @override
  _i7.Future<_i5.PaginatedResponse<_i4.TeamSearchItem>> getAllTeamsPaginated({
    int? page,
    int? pageSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAllTeamsPaginated, [], {
              #page: page,
              #pageSize: pageSize,
            }),
            returnValue:
                _i7.Future<_i5.PaginatedResponse<_i4.TeamSearchItem>>.value(
                  _FakePaginatedResponse_3<_i4.TeamSearchItem>(
                    this,
                    Invocation.method(#getAllTeamsPaginated, [], {
                      #page: page,
                      #pageSize: pageSize,
                    }),
                  ),
                ),
          )
          as _i7.Future<_i5.PaginatedResponse<_i4.TeamSearchItem>>);
}
