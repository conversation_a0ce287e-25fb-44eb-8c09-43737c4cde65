// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in nextsportz_v2/test/features/teams/data/repositories/teams_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i6;
import 'package:nextsportz_v2/core/models/paginated_response.dart' as _i3;
import 'package:nextsportz_v2/features/teams/data/datasources/teams_datasource.dart'
    as _i4;
import 'package:nextsportz_v2/features/teams/domain/entities/team.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTeam_0 extends _i1.SmartFake implements _i2.Team {
  _FakeTeam_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePaginatedResponse_1<T> extends _i1.SmartFake
    implements _i3.PaginatedResponse<T> {
  _FakePaginatedResponse_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [TeamsDatasource].
///
/// See the documentation for Mockito's code generation for more information.
class MockTeamsDatasource extends _i1.Mock implements _i4.TeamsDatasource {
  MockTeamsDatasource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<List<_i2.TeamSearchItem>> getMyTeams() =>
      (super.noSuchMethod(
            Invocation.method(#getMyTeams, []),
            returnValue: _i5.Future<List<_i2.TeamSearchItem>>.value(
              <_i2.TeamSearchItem>[],
            ),
          )
          as _i5.Future<List<_i2.TeamSearchItem>>);

  @override
  _i5.Future<_i2.Team> getTeamById(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamById, [teamId]),
            returnValue: _i5.Future<_i2.Team>.value(
              _FakeTeam_0(this, Invocation.method(#getTeamById, [teamId])),
            ),
          )
          as _i5.Future<_i2.Team>);

  @override
  _i5.Future<_i2.Team> createTeam({
    required String? name,
    required String? description,
    String? logo,
    String? slogan,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createTeam, [], {
              #name: name,
              #description: description,
              #logo: logo,
              #slogan: slogan,
            }),
            returnValue: _i5.Future<_i2.Team>.value(
              _FakeTeam_0(
                this,
                Invocation.method(#createTeam, [], {
                  #name: name,
                  #description: description,
                  #logo: logo,
                  #slogan: slogan,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.Team>);

  @override
  _i5.Future<_i2.Team> updateTeam({
    required String? teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateTeam, [], {
              #teamId: teamId,
              #name: name,
              #description: description,
              #logo: logo,
              #slogan: slogan,
            }),
            returnValue: _i5.Future<_i2.Team>.value(
              _FakeTeam_0(
                this,
                Invocation.method(#updateTeam, [], {
                  #teamId: teamId,
                  #name: name,
                  #description: description,
                  #logo: logo,
                  #slogan: slogan,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.Team>);

  @override
  _i5.Future<void> deleteTeam(String? teamId) =>
      (super.noSuchMethod(
            Invocation.method(#deleteTeam, [teamId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> invitePlayer({
    required String? teamId,
    required String? playerId,
    String? message,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#invitePlayer, [], {
              #teamId: teamId,
              #playerId: playerId,
              #message: message,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> acceptInvitation(String? invitationId) =>
      (super.noSuchMethod(
            Invocation.method(#acceptInvitation, [invitationId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> declineInvitation(String? invitationId) =>
      (super.noSuchMethod(
            Invocation.method(#declineInvitation, [invitationId]),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> removeMember({
    required String? teamId,
    required String? memberId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#removeMember, [], {
              #teamId: teamId,
              #memberId: memberId,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> updateMemberRole({
    required String? teamId,
    required String? memberId,
    required String? role,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateMemberRole, [], {
              #teamId: teamId,
              #memberId: memberId,
              #role: role,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<List<_i2.TeamInvitation>> getPendingInvitations() =>
      (super.noSuchMethod(
            Invocation.method(#getPendingInvitations, []),
            returnValue: _i5.Future<List<_i2.TeamInvitation>>.value(
              <_i2.TeamInvitation>[],
            ),
          )
          as _i5.Future<List<_i2.TeamInvitation>>);

  @override
  _i5.Future<String> uploadTeamLogo(dynamic imageFile) =>
      (super.noSuchMethod(
            Invocation.method(#uploadTeamLogo, [imageFile]),
            returnValue: _i5.Future<String>.value(
              _i6.dummyValue<String>(
                this,
                Invocation.method(#uploadTeamLogo, [imageFile]),
              ),
            ),
          )
          as _i5.Future<String>);

  @override
  _i5.Future<List<_i2.Team>> getTeamsLeaderboard({int? limit}) =>
      (super.noSuchMethod(
            Invocation.method(#getTeamsLeaderboard, [], {#limit: limit}),
            returnValue: _i5.Future<List<_i2.Team>>.value(<_i2.Team>[]),
          )
          as _i5.Future<List<_i2.Team>>);

  @override
  _i5.Future<List<_i2.Team>> searchTeams({
    required String? query,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#searchTeams, [], {
              #query: query,
              #limit: limit,
              #offset: offset,
            }),
            returnValue: _i5.Future<List<_i2.Team>>.value(<_i2.Team>[]),
          )
          as _i5.Future<List<_i2.Team>>);

  @override
  _i5.Future<List<_i2.Team>> getAllTeams({int? limit, int? offset}) =>
      (super.noSuchMethod(
            Invocation.method(#getAllTeams, [], {
              #limit: limit,
              #offset: offset,
            }),
            returnValue: _i5.Future<List<_i2.Team>>.value(<_i2.Team>[]),
          )
          as _i5.Future<List<_i2.Team>>);

  @override
  _i5.Future<_i3.PaginatedResponse<_i2.TeamSearchItem>> searchTeamsPaginated({
    String? query,
    int? page,
    int? pageSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#searchTeamsPaginated, [], {
              #query: query,
              #page: page,
              #pageSize: pageSize,
            }),
            returnValue:
                _i5.Future<_i3.PaginatedResponse<_i2.TeamSearchItem>>.value(
                  _FakePaginatedResponse_1<_i2.TeamSearchItem>(
                    this,
                    Invocation.method(#searchTeamsPaginated, [], {
                      #query: query,
                      #page: page,
                      #pageSize: pageSize,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i3.PaginatedResponse<_i2.TeamSearchItem>>);

  @override
  _i5.Future<_i3.PaginatedResponse<_i2.TeamSearchItem>> getAllTeamsPaginated({
    int? page,
    int? pageSize,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getAllTeamsPaginated, [], {
              #page: page,
              #pageSize: pageSize,
            }),
            returnValue:
                _i5.Future<_i3.PaginatedResponse<_i2.TeamSearchItem>>.value(
                  _FakePaginatedResponse_1<_i2.TeamSearchItem>(
                    this,
                    Invocation.method(#getAllTeamsPaginated, [], {
                      #page: page,
                      #pageSize: pageSize,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i3.PaginatedResponse<_i2.TeamSearchItem>>);
}
