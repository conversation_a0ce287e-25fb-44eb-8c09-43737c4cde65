import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/config/theme.dart';
import '../../../../core/providers/theme_provider.dart' as theme_providers;
import '../../../../utils/color.dart';
import '../../teams_providers.dart';
import '../../domain/entities/team.dart';
import 'invite_player_screen.dart';

class TeamDetailsScreen extends ConsumerStatefulWidget {
  final String teamId;
  final bool isPublicView;

  const TeamDetailsScreen({
    super.key,
    required this.teamId,
    this.isPublicView = false,
  });

  @override
  ConsumerState<TeamDetailsScreen> createState() => _TeamDetailsScreenState();
}

class _TeamDetailsScreenState extends ConsumerState<TeamDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final teamAsync = ref.watch(teamByIdProvider(widget.teamId));
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);

    return Scaffold(
      backgroundColor: primaryColor,
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: teamAsync.when(
          data:
              (team) => Text(
                team.name,
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
          loading:
              () => const Text(
                'Team Details',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
          error:
              (_, __) => const Text(
                'Team Details',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
        ),
        actions: [
          if (!widget.isPublicView) ...[
            IconButton(
              icon: const Icon(Icons.person_add, color: Colors.white),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => InvitePlayerScreen(teamId: widget.teamId),
                  ),
                );
              },
            ),
          ] else ...[
            IconButton(
              icon: const Icon(Icons.share, color: Colors.white),
              onPressed: () => _shareTeam(),
            ),
          ],
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: accentColor,
          labelColor: Colors.white,
          unselectedLabelColor: greyColor,
          labelStyle: const TextStyle(fontFamily: 'Gilroy_Bold', fontSize: 14),
          unselectedLabelStyle: const TextStyle(
            fontFamily: 'Gilroy_Medium',
            fontSize: 14,
          ),
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Members'),
            Tab(text: 'Stats'),
          ],
        ),
      ),
      body: teamAsync.when(
        data:
            (team) => TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(team),
                _buildMembersTab(team),
                _buildStatsTab(team),
              ],
            ),
        loading:
            () => Center(
              child: CircularProgressIndicator(
                color: NextSportzTheme.getAccentColor(
                  ref.watch(theme_providers.isDarkModeProvider),
                ),
              ),
            ),
        error:
            (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 64),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading team',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed:
                        () => ref.refresh(teamByIdProvider(widget.teamId)),
                    child: Text(
                      'Retry',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color: NextSportzTheme.getAccentColor(
                          ref.watch(theme_providers.isDarkModeProvider),
                        ),
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
      ),
    );
  }

  Widget _buildOverviewTab(Team team) {
    // later get invitations with separate API
    final invitations = [];
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);
    final lightGreyColor = NextSportzTheme.getLightGreyColor(isDark);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Hero Team Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  accentColor.withOpacity(0.1),
                  secondaryColor.withOpacity(0.05),
                ],
              ),
              border: Border.all(color: accentColor.withOpacity(0.2), width: 1),
            ),
            child: Column(
              children: [
                // Team Logo with Glow Effect
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(40),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        accentColor.withOpacity(0.3),
                        accentColor.withOpacity(0.1),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: accentColor.withOpacity(0.3),
                        blurRadius: 20,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child:
                      team.logoUrl.isNotEmpty
                          ? ClipRRect(
                            borderRadius: BorderRadius.circular(40),
                            child: CachedNetworkImage(
                              imageUrl: team.logoUrl,
                              fit: BoxFit.cover,
                              width: 80,
                              height: 80,
                              placeholder:
                                  (context, url) => Container(
                                    decoration: BoxDecoration(
                                      color: lightGreyColor.withOpacity(0.3),
                                      borderRadius: BorderRadius.circular(40),
                                    ),
                                    child: Center(
                                      child: CircularProgressIndicator(
                                        color: accentColor,
                                        strokeWidth: 2,
                                      ),
                                    ),
                                  ),
                              errorWidget:
                                  (context, url, error) => Container(
                                    decoration: BoxDecoration(
                                      color: lightGreyColor.withOpacity(0.3),
                                      borderRadius: BorderRadius.circular(40),
                                    ),
                                    child: Icon(
                                      Icons.sports_soccer,
                                      color: accentColor,
                                      size: 40,
                                    ),
                                  ),
                            ),
                          )
                          : Icon(
                            Icons.sports_soccer,
                            color: accentColor,
                            size: 40,
                          ),
                ),
                const SizedBox(height: 16),
                Text(
                  team.name,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 22,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: accentColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Team Code: ${team.teamCode}',
                    style: TextStyle(
                      fontFamily: 'Gilroy_Medium',
                      color: accentColor,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      team.isActive ? Icons.check_circle : Icons.cancel,
                      color:
                          team.isActive
                              ? NextSportzTheme.lightAccent
                              : Colors.red,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      team.isActive ? 'Active Team' : 'Inactive Team',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color:
                            team.isActive
                                ? NextSportzTheme.lightAccent
                                : Colors.red,
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Quick Stats Cards
          Row(
            children: [
              Expanded(
                child: _buildQuickStatCard(
                  'Members',
                  '${team.members.length}',
                  Icons.people,
                  accentColor,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickStatCard(
                  'Matches',
                  '${team.stats.totalMatches}',
                  Icons.sports_soccer,
                  NextSportzTheme.lightAccentSecondary,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildQuickStatCard(
                  'Win Rate',
                  '${team.stats.winRate.toStringAsFixed(0)}%',
                  Icons.trending_up,
                  team.stats.winRate >= 50
                      ? NextSportzTheme.lightAccent
                      : NextSportzTheme.lightAccentSecondary,
                ),
              ),
            ],
          ),

          // Challenge section for public view
          if (widget.isPublicView) ...[
            const SizedBox(height: 16),
            _buildChallengeSection(team, accentColor, secondaryColor),
          ],
          const SizedBox(height: 16),
          // Description Section
          _buildModernSection(
            'About',
            Icons.description,
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: secondaryColor.withOpacity(0.3),
                border: Border.all(
                  color: lightGreyColor.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Text(
                team.description.isNotEmpty
                    ? team.description
                    : 'No description available',
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
            ),
            accentColor,
            secondaryColor,
          ),

          if (team.slogan != null && team.slogan!.isNotEmpty) ...[
            const SizedBox(height: 16),
            // Team Slogan with Modern Design
            _buildModernSection(
              'Team Slogan',
              Icons.format_quote,
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      accentColor.withOpacity(0.1),
                      accentColor.withOpacity(0.05),
                    ],
                  ),
                  border: Border.all(
                    color: accentColor.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: accentColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.format_quote,
                        color: accentColor,
                        size: 18,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        '"${team.slogan}"',
                        style: TextStyle(
                          fontFamily: 'Gilroy_Bold',
                          color: Colors.white,
                          fontSize: 15,
                          fontStyle: FontStyle.italic,
                          height: 1.3,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              accentColor,
              secondaryColor,
            ),
          ],

          const SizedBox(height: 16),

          // Team Info
          _buildModernSection(
            'Team Info',
            Icons.info,
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: secondaryColor.withOpacity(0.2),
                border: Border.all(
                  color: lightGreyColor.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  _buildModernInfoRow(
                    'Created',
                    _formatDate(team.created),
                    Icons.calendar_today,
                    greyColor,
                  ),
                  const SizedBox(height: 12),
                  _buildModernInfoRow(
                    'Status',
                    team.isActive ? 'Active' : 'Inactive',
                    team.isActive ? Icons.check_circle : Icons.cancel,
                    team.isActive ? NextSportzTheme.lightAccent : Colors.red,
                  ),
                  const SizedBox(height: 12),
                  _buildModernInfoRow(
                    'Team Code',
                    team.teamCode,
                    Icons.qr_code,
                    accentColor,
                  ),
                ],
              ),
            ),
            accentColor,
            secondaryColor,
          ),

          if (invitations.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildModernSection(
              'Pending Invitations',
              Icons.mail_outline,
              Column(
                children:
                    invitations.map((invitation) {
                      return _buildInvitationItem(invitation);
                    }).toList(),
              ),
              accentColor,
              secondaryColor,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMembersTab(Team team) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);
    final lightGreyColor = NextSportzTheme.getLightGreyColor(isDark);

    if (team.members.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: secondaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.group_off,
                size: 48,
                color: Colors.white.withOpacity(0.4),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'No Members Yet',
              style: TextStyle(
                fontFamily: 'Gilroy_Bold',
                color: Colors.white.withOpacity(0.9),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Invite players to join your team',
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white.withOpacity(0.6),
                fontSize: 13,
              ),
            ),
            const SizedBox(height: 16),
            if (!widget.isPublicView)
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => InvitePlayerScreen(teamId: team.id),
                    ),
                  );
                },
                icon: const Icon(Icons.person_add, size: 16),
                label: const Text('Invite Players'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: accentColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  textStyle: const TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    fontSize: 13,
                  ),
                ),
              ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Team Summary Header
        Container(
          margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: secondaryColor.withOpacity(0.1),
            border: Border.all(color: Colors.white.withOpacity(0.05), width: 1),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.group, color: accentColor, size: 18),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Team Members',
                      style: const TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        color: Colors.white,
                        fontSize: 15,
                      ),
                    ),
                    Text(
                      '${team.members.length} members',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color: Colors.white.withOpacity(0.6),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              if (!widget.isPublicView)
                Container(
                  decoration: BoxDecoration(
                    color: accentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: IconButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => InvitePlayerScreen(teamId: team.id),
                        ),
                      );
                    },
                    icon: Icon(Icons.person_add, color: accentColor, size: 18),
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ),
            ],
          ),
        ),

        // Members List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: team.members.length,
            itemBuilder: (context, index) {
              final member = team.members[index];
              return _buildMemberCard(member);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatsTab(Team team) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final primaryColor = NextSportzTheme.getPrimaryColor(isDark);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);
    final greyColor = NextSportzTheme.getGreyColor(isDark);
    final lightGreyColor = NextSportzTheme.getLightGreyColor(isDark);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Performance Overview Cards
          Row(
            children: [
              Expanded(
                child: _buildCompactStatCard(
                  'Matches',
                  '${team.stats.totalMatches}',
                  Icons.sports_soccer,
                  accentColor,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildCompactStatCard(
                  'Win Rate',
                  '${team.stats.winRate.toStringAsFixed(1)}%',
                  Icons.trending_up,
                  team.stats.winRate >= 50
                      ? NextSportzTheme.lightAccent
                      : NextSportzTheme.lightAccentSecondary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          Row(
            children: [
              Expanded(
                child: _buildCompactStatCard(
                  'Goals',
                  '${team.stats.scoredGoals}',
                  Icons.sports_soccer,
                  NextSportzTheme.lightAccentSecondary,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildCompactStatCard(
                  'Goal Diff',
                  '${team.stats.goalDifference >= 0 ? '+' : ''}${team.stats.goalDifference}',
                  Icons.compare_arrows,
                  team.stats.goalDifference >= 0
                      ? NextSportzTheme.lightAccent
                      : Colors.red,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Detailed Performance Section
          _buildSection(
            'Match Performance',
            Icons.analytics,
            Column(
              children: [
                _buildDetailedStatRow(
                  'Total Matches',
                  '${team.stats.totalMatches}',
                  Icons.sports_soccer,
                ),
                _buildDetailedStatRow(
                  'Wins',
                  '${team.stats.wins}',
                  Icons.emoji_events,
                  color: NextSportzTheme.lightAccent,
                ),
                _buildDetailedStatRow(
                  'Draws',
                  '${team.stats.draws}',
                  Icons.horizontal_rule,
                  color: NextSportzTheme.lightAccentSecondary,
                ),
                _buildDetailedStatRow(
                  'Losses',
                  '${team.stats.losses}',
                  Icons.close,
                  color: Colors.red,
                ),
                _buildDetailedStatRow(
                  'Win Rate',
                  '${team.stats.winRate.toStringAsFixed(1)}%',
                  Icons.trending_up,
                  color:
                      team.stats.winRate >= 50
                          ? NextSportzTheme.lightAccent
                          : NextSportzTheme.lightAccentSecondary,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Goals Analysis Section
          _buildSection(
            'Goals Analysis',
            Icons.sports_soccer,
            Column(
              children: [
                _buildDetailedStatRow(
                  'Goals Scored',
                  '${team.stats.scoredGoals}',
                  Icons.sports_soccer,
                  color: NextSportzTheme.lightAccent,
                ),
                _buildDetailedStatRow(
                  'Goals Conceded',
                  '${team.stats.concededGoals}',
                  Icons.sports_soccer,
                  color: Colors.red,
                ),
                _buildDetailedStatRow(
                  'Goal Difference',
                  '${team.stats.goalDifference >= 0 ? '+' : ''}${team.stats.goalDifference}',
                  Icons.compare_arrows,
                  color:
                      team.stats.goalDifference >= 0
                          ? NextSportzTheme.lightAccent
                          : Colors.red,
                ),
                if (team.stats.totalMatches > 0)
                  _buildDetailedStatRow(
                    'Avg Goals/Match',
                    '${(team.stats.scoredGoals / team.stats.totalMatches).toStringAsFixed(1)}',
                    Icons.calculate,
                    color: NextSportzTheme.lightAccentSecondary,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, Widget content) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);
    final accentColor = NextSportzTheme.getAccentColor(isDark);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: secondaryColor.withOpacity(0.5),
        border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: accentColor, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          content,
        ],
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: NextSportzTheme.getAccentColor(
            ref.watch(theme_providers.isDarkModeProvider),
          ),
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: Colors.white,
            fontSize: 20,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontFamily: 'Gilroy_Medium',
            color: grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: grey,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: grey,
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactStatCard(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: secondaryColor.withOpacity(0.08),
        border: Border.all(color: color.withOpacity(0.2), width: 0.5),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 6),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.6),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedStatRow(
    String label,
    String value,
    IconData icon, {
    Color? color,
  }) {
    final statColor =
        color ??
        NextSportzTheme.getAccentColor(
          ref.watch(theme_providers.isDarkModeProvider),
        );

    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: statColor.withOpacity(0.15),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, color: statColor, size: 14),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: Colors.white.withOpacity(0.7),
                fontSize: 13,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: statColor,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemberCard(TeamMember member) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: secondaryColor.withOpacity(0.08),
        border: Border.all(
          color: _getRoleColor(member.role).withOpacity(0.2),
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getRoleColor(member.role).withOpacity(0.15),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: _getRoleColor(member.role).withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                member.userName.isNotEmpty
                    ? member.userName[0].toUpperCase()
                    : 'U',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: _getRoleColor(member.role),
                  fontSize: 16,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  member.userName,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  member.playerName,
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 6),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: _getRoleColor(member.role).withOpacity(0.15),
                      ),
                      child: Text(
                        _getRoleDisplayName(member.role),
                        style: TextStyle(
                          fontFamily: 'Gilroy_Medium',
                          color: _getRoleColor(member.role),
                          fontSize: 10,
                        ),
                      ),
                    ),
                    const SizedBox(width: 6),
                    Icon(
                      member.isActive ? Icons.check_circle : Icons.cancel,
                      color:
                          member.isActive
                              ? NextSportzTheme.lightAccent
                              : Colors.red,
                      size: 14,
                    ),
                  ],
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _formatDate(member.joinedAt),
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white.withOpacity(0.5),
                  fontSize: 10,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                'Joined',
                style: TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  color: Colors.white.withOpacity(0.4),
                  fontSize: 9,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationItem(TeamInvitation invitation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: NextSportzTheme.lightAccentSecondary.withOpacity(0.1),
        border: Border.all(
          color: NextSportzTheme.lightAccentSecondary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: NextSportzTheme.lightAccentSecondary.withOpacity(
              0.2,
            ),
            child: Text(
              invitation.invitedUserName[0].toUpperCase(),
              style: TextStyle(
                fontFamily: 'Gilroy_Bold',
                color: NextSportzTheme.lightAccentSecondary,
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  invitation.invitedUserName,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
                Text(
                  'Invited ${_formatDate(invitation.createdAt)}',
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: NextSportzTheme.lightAccentSecondary.withOpacity(0.2),
            ),
            child: Text(
              'Pending',
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: NextSportzTheme.lightAccentSecondary,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopPerformerCard(TeamMember member) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: secondaryColor.withOpacity(0.06),
        border: Border.all(
          color: _getRoleColor(member.role).withOpacity(0.2),
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getRoleColor(member.role).withOpacity(0.15),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: _getRoleColor(member.role).withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                member.playerName.isNotEmpty
                    ? member.playerName[0].toUpperCase()
                    : 'P',
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: _getRoleColor(member.role),
                  fontSize: 14,
                ),
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  member.playerName,
                  style: const TextStyle(
                    fontFamily: 'Gilroy_Bold',
                    color: Colors.white,
                    fontSize: 13,
                  ),
                ),
                Text(
                  member.userName,
                  style: TextStyle(
                    fontFamily: 'Gilroy_Medium',
                    color: Colors.white.withOpacity(0.5),
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: _getRoleColor(member.role).withOpacity(0.15),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              _getRoleDisplayName(member.role),
              style: TextStyle(
                fontFamily: 'Gilroy_Medium',
                color: _getRoleColor(member.role),
                fontSize: 9,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Modern section builder with theme consistency
  Widget _buildModernSection(
    String title,
    IconData icon,
    Widget content,
    Color accentColor,
    Color secondaryColor,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: accentColor.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: accentColor, size: 16),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontFamily: 'Gilroy_Bold',
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          content,
        ],
      ),
    );
  }

  // Quick stat card for overview
  Widget _buildQuickStatCard(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final isDark = ref.watch(theme_providers.isDarkModeProvider);
    final secondaryColor = NextSportzTheme.getSecondaryColor(isDark);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: secondaryColor.withOpacity(0.3),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 18),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontFamily: 'Gilroy_Bold',
              color: Colors.white,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.7),
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  // Modern info row for team details
  Widget _buildModernInfoRow(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.15),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(icon, color: color, size: 14),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontFamily: 'Gilroy_Medium',
              color: Colors.white.withOpacity(0.7),
              fontSize: 13,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontFamily: 'Gilroy_Bold',
            color: color,
            fontSize: 13,
          ),
        ),
      ],
    );
  }

  Color _getRoleColor(String role) {
    switch (role) {
      case 'captain':
        return const Color(0xFFFFD700); // Gold color for captain
      case 'vice_captain':
        return NextSportzTheme.lightAccentSecondary; // Orange from theme
      default:
        return NextSportzTheme.lightAccent; // Green from theme
    }
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'captain':
        return 'Captain';
      case 'vice_captain':
        return 'Vice Captain';
      default:
        return 'Member';
    }
  }

  void _shareTeam() {
    // TODO: Implement team sharing functionality
    // This could share a link to the public team page
  }

  void _challengeTeam(Team team) {
    // TODO: Implement challenge team functionality
    // Navigate to challenge creation screen with pre-filled team
  }

  Widget _buildChallengeSection(
    Team team,
    Color accentColor,
    Color secondaryColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            accentColor.withValues(alpha: 0.1),
            accentColor.withValues(alpha: 0.05),
          ],
        ),
        border: Border.all(color: accentColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: accentColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.sports_mma, color: accentColor, size: 18),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Ready for a Challenge?',
                      style: const TextStyle(
                        fontFamily: 'Gilroy_Bold',
                        color: Colors.white,
                        fontSize: 15,
                      ),
                    ),
                    Text(
                      'Challenge ${team.name} to a match',
                      style: TextStyle(
                        fontFamily: 'Gilroy_Medium',
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _challengeTeam(team),
              icon: const Icon(Icons.sports_mma, size: 16),
              label: const Text('Send Challenge'),
              style: ElevatedButton.styleFrom(
                backgroundColor: accentColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                textStyle: const TextStyle(
                  fontFamily: 'Gilroy_Medium',
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
