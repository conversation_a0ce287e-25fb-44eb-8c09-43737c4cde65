import '../../domain/entities/team.dart';
import '../../../../core/networking/api_client.dart';
import '../../../../core/networking/api_const.dart';
import '../dto/team_invitation_dto.dart';
import '../dto/team_request_dto.dart';
import 'team_invitations_datasource.dart';

/// Remote data source implementation for team invitations operations
class TeamInvitationsRemoteDataSource implements TeamInvitationsDatasource {
  final ApiClient apiClient;

  TeamInvitationsRemoteDataSource(this.apiClient);

  @override
  Future<List<TeamInvitation>> getPendingInvitations() async {
    final response = await apiClient.get(ApiConst.teamInvitationsEndpoint);
    final List<dynamic> data = response['data'] ?? response;
    return data.map((json) {
      final dto = TeamInvitationDto.fromJson(json);
      return _mapInvitationDtoToEntity(dto);
    }).toList();
  }

  @override
  Future<List<TeamInvitation>> getTeamInvitations(String teamId) async {
    final endpoint = '${ApiConst.teamInvitationsEndpoint}?team_id=$teamId';
    final response = await apiClient.get(endpoint);
    final List<dynamic> data = response['data'] ?? response;
    return data.map((json) {
      final dto = TeamInvitationDto.fromJson(json);
      return _mapInvitationDtoToEntity(dto);
    }).toList();
  }

  @override
  Future<TeamInvitation> getInvitation(String invitationId) async {
    final endpoint = ApiConst.teamInvitationDetailEndpoint.replaceAll(
      '{invitationId}',
      invitationId,
    );
    final response = await apiClient.get(endpoint);
    final dto = TeamInvitationDto.fromJson(response);
    return _mapInvitationDtoToEntity(dto);
  }

  @override
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    String role = 'Player',
    String? message,
  }) async {
    final endpoint = ApiConst.teamInvitePlayerEndpoint.replaceAll(
      '{teamId}',
      teamId,
    );
    final requestDto = InvitePlayerRequestDto(
      playerId: playerId,
      message: message,
    );

    await apiClient.post(endpoint, data: requestDto.toJson());
  }

  @override
  Future<void> acceptInvitation(String invitationId) async {
    final endpoint = ApiConst.teamInvitationResponseEndpoint.replaceAll(
      '{invitationId}',
      invitationId,
    );
    final requestDto = InvitationResponseRequestDto(action: 'accept');

    await apiClient.post(endpoint, data: requestDto.toJson());
  }

  @override
  Future<void> declineInvitation(String invitationId) async {
    final endpoint = ApiConst.teamInvitationResponseEndpoint.replaceAll(
      '{invitationId}',
      invitationId,
    );
    final requestDto = InvitationResponseRequestDto(action: 'decline');

    await apiClient.post(endpoint, data: requestDto.toJson());
  }

  @override
  Future<int> getTeamInvitationsCount(String teamId) async {
    final endpoint =
        '${ApiConst.teamInvitationsEndpoint}/count?team_id=$teamId';
    final response = await apiClient.get(endpoint);
    return response['count'] as int? ?? 0;
  }

  // Helper method to map DTO to entity
  TeamInvitation _mapInvitationDtoToEntity(TeamInvitationDto dto) {
    return TeamInvitation(
      id: dto.id,
      teamId: dto.teamId,
      invitedUserId: dto.invitedUserId,
      invitedUserName: dto.invitedUserName,
      invitedUserEmail: dto.invitedUserEmail,
      invitedUserPhone: dto.invitedUserPhone,
      status: dto.status,
      createdAt: DateTime.parse(dto.createdAt),
      respondedAt:
          dto.respondedAt != null ? DateTime.parse(dto.respondedAt!) : null,
    );
  }
}
