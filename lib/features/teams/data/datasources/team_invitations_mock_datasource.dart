import '../../domain/entities/team.dart';
import 'team_invitations_datasource.dart';

/// Mock data source implementation for team invitations operations
class TeamInvitationsMockDataSource implements TeamInvitationsDatasource {
  static final List<TeamInvitation> _mockPendingInvitations = [
    TeamInvitation(
      id: 'invitation1',
      teamId: '1',
      invitedUserId: 'user6',
      invitedUserName: '<PERSON>',
      invitedUserEmail: '<EMAIL>',
      invitedUserPhone: '+1234567890',
      status: 'pending',
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      respondedAt: null,
    ),
    TeamInvitation(
      id: 'invitation2',
      teamId: '2',
      invitedUserId: 'user7',
      invitedUserName: '<PERSON>',
      invitedUserEmail: '<EMAIL>',
      invitedUserPhone: '+1234567891',
      status: 'pending',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      respondedAt: null,
    ),
  ];

  static final Map<String, List<TeamInvitation>> _mockTeamInvitations = {
    '1': [
      TeamInvitation(
        id: 'invitation1',
        teamId: '1',
        invitedUserId: 'user6',
        invitedUserName: '<PERSON>',
        invitedUserEmail: '<EMAIL>',
        invitedUserPhone: '+1234567890',
        status: 'pending',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        respondedAt: null,
      ),
      TeamInvitation(
        id: 'invitation3',
        teamId: '1',
        invitedUserId: 'user8',
        invitedUserName: 'Chris Wilson',
        invitedUserEmail: '<EMAIL>',
        invitedUserPhone: '+1234567892',
        status: 'accepted',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        respondedAt: DateTime.now().subtract(const Duration(days: 4)),
      ),
    ],
    '2': [
      TeamInvitation(
        id: 'invitation2',
        teamId: '2',
        invitedUserId: 'user7',
        invitedUserName: 'Emma Davis',
        invitedUserEmail: '<EMAIL>',
        invitedUserPhone: '+1234567891',
        status: 'pending',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        respondedAt: null,
      ),
    ],
  };

  @override
  Future<List<TeamInvitation>> getPendingInvitations() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));
    return _mockPendingInvitations
        .where((inv) => inv.status == 'pending')
        .toList();
  }

  @override
  Future<List<TeamInvitation>> getTeamInvitations(String teamId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 250));
    return _mockTeamInvitations[teamId] ?? [];
  }

  @override
  Future<TeamInvitation> getInvitation(String invitationId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 200));

    // Search in all invitations
    for (final invitations in _mockTeamInvitations.values) {
      for (final invitation in invitations) {
        if (invitation.id == invitationId) {
          return invitation;
        }
      }
    }

    throw Exception('Invitation not found');
  }

  @override
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    String role = 'Player',
    String? message,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 400));

    final newInvitation = TeamInvitation(
      id: 'invitation_${DateTime.now().millisecondsSinceEpoch}',
      teamId: teamId,
      invitedUserId: playerId,
      invitedUserName: 'New Player',
      invitedUserEmail: '<EMAIL>',
      status: 'pending',
      createdAt: DateTime.now(),
      respondedAt: null,
    );

    _mockTeamInvitations[teamId] ??= [];
    _mockTeamInvitations[teamId]!.add(newInvitation);
    _mockPendingInvitations.add(newInvitation);
  }

  @override
  Future<void> acceptInvitation(String invitationId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));

    // Update invitation status
    for (final invitations in _mockTeamInvitations.values) {
      for (int i = 0; i < invitations.length; i++) {
        if (invitations[i].id == invitationId) {
          invitations[i] = TeamInvitation(
            id: invitations[i].id,
            teamId: invitations[i].teamId,
            invitedUserId: invitations[i].invitedUserId,
            invitedUserName: invitations[i].invitedUserName,
            invitedUserEmail: invitations[i].invitedUserEmail,
            invitedUserPhone: invitations[i].invitedUserPhone,
            status: 'accepted',
            createdAt: invitations[i].createdAt,
            respondedAt: DateTime.now(),
          );
          break;
        }
      }
    }

    // Remove from pending invitations
    _mockPendingInvitations.removeWhere((inv) => inv.id == invitationId);
  }

  @override
  Future<void> declineInvitation(String invitationId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));

    // Update invitation status
    for (final invitations in _mockTeamInvitations.values) {
      for (int i = 0; i < invitations.length; i++) {
        if (invitations[i].id == invitationId) {
          invitations[i] = TeamInvitation(
            id: invitations[i].id,
            teamId: invitations[i].teamId,
            invitedUserId: invitations[i].invitedUserId,
            invitedUserName: invitations[i].invitedUserName,
            invitedUserEmail: invitations[i].invitedUserEmail,
            invitedUserPhone: invitations[i].invitedUserPhone,
            status: 'declined',
            createdAt: invitations[i].createdAt,
            respondedAt: DateTime.now(),
          );
          break;
        }
      }
    }

    // Remove from pending invitations
    _mockPendingInvitations.removeWhere((inv) => inv.id == invitationId);
  }

  @override
  Future<int> getTeamInvitationsCount(String teamId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 150));
    final invitations = _mockTeamInvitations[teamId] ?? [];
    return invitations.where((inv) => inv.status == 'pending').length;
  }
}
