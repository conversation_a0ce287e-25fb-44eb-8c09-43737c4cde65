import 'dart:io';
import 'package:dio/dio.dart';
import '../../domain/entities/team.dart';
import '../../../../core/networking/api_client.dart';
import '../../../../core/networking/api_const.dart';
import '../../../../core/networking/exception.dart';
import '../dto/team_dto.dart';
import '../dto/team_invitation_dto.dart';
import '../dto/team_request_dto.dart';
import '../dto/team_search_item_dto.dart';
import '../../../../core/models/paginated_response.dart';
import 'teams_datasource.dart';

/// Remote data source implementation for teams operations
class TeamsRemoteDataSource implements TeamsDatasource {
  final ApiClient apiClient;

  TeamsRemoteDataSource(this.apiClient);

  @override
  Future<List<TeamSearchItem>> getMyTeams() async {
    try {
      final response = await apiClient.get(ApiConst.myTeamsEndpoint);

      // Handle the response format - if it's a simple array like other team endpoints
      final List<dynamic> data = response['data'] ?? response;

      // Convert the full team data to TeamSearchItem format
      final teams =
          data.map((json) {
            return TeamSearchItem.fromJson(json);
          }).toList();

      return teams;
    } catch (e) {
      if (e is DioExceptionHandle) {
        rethrow;
      }
      if (e is DioException) {
        throw DioExceptionHandle.fromDioError(e);
      }
      throw Exception('Failed to get my teams: ${e.toString()}');
    }
  }

  @override
  Future<Team> getTeamById(String teamId) async {
    final endpoint = ApiConst.teamDetailEndpoint.replaceAll('{teamId}', teamId);
    final response = await apiClient.get(endpoint);
    final data = response['data'] ?? response;
    final dto = TeamDto.fromJson(data);
    return _mapDtoToEntity(dto);
  }

  @override
  Future<Team> createTeam({
    required String name,
    required String description,
    String? logo,
    String? slogan,
  }) async {
    final requestDto = CreateTeamRequestDto(
      name: name,
      description: description,
      logo: logo,
      slogan: slogan,
    );

    final response = await apiClient.post(
      ApiConst.teamsEndpoint,
      data: requestDto.toJson(),
    );
    final dto = TeamDto.fromJson(response);
    return _mapDtoToEntity(dto);
  }

  @override
  Future<Team> updateTeam({
    required String teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  }) async {
    final endpoint = ApiConst.teamDetailEndpoint.replaceAll('{teamId}', teamId);
    final requestDto = UpdateTeamRequestDto(
      name: name,
      description: description,
      logo: logo,
      slogan: slogan,
    );

    final response = await apiClient.put(endpoint, data: requestDto.toJson());
    final dto = TeamDto.fromJson(response);
    return _mapDtoToEntity(dto);
  }

  @override
  Future<void> deleteTeam(String teamId) async {
    final endpoint = ApiConst.teamDetailEndpoint.replaceAll('{teamId}', teamId);
    await apiClient.delete(endpoint);
  }

  @override
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    String? message,
  }) async {
    final endpoint = ApiConst.teamInvitePlayerEndpoint.replaceAll(
      '{teamId}',
      teamId,
    );
    final requestDto = InvitePlayerRequestDto(
      playerId: playerId,
      message: message,
    );

    await apiClient.post(endpoint, data: requestDto.toJson());
  }

  @override
  Future<void> acceptInvitation(String invitationId) async {
    final endpoint = ApiConst.teamInvitationResponseEndpoint.replaceAll(
      '{invitationId}',
      invitationId,
    );
    final requestDto = InvitationResponseRequestDto(action: 'accept');

    await apiClient.post(endpoint, data: requestDto.toJson());
  }

  @override
  Future<void> declineInvitation(String invitationId) async {
    final endpoint = ApiConst.teamInvitationResponseEndpoint.replaceAll(
      '{invitationId}',
      invitationId,
    );
    final requestDto = InvitationResponseRequestDto(action: 'decline');

    await apiClient.post(endpoint, data: requestDto.toJson());
  }

  @override
  Future<void> removeMember({
    required String teamId,
    required String memberId,
  }) async {
    final endpoint = '/api/teams/$teamId/members/$memberId';

    await apiClient.delete(endpoint);
  }

  @override
  Future<void> updateMemberRole({
    required String teamId,
    required String memberId,
    required String role,
  }) async {
    final endpoint = ApiConst.teamMemberRoleEndpoint
        .replaceAll('{teamId}', teamId)
        .replaceAll('{memberId}', memberId);

    final requestData = {'role': role};
    await apiClient.put(endpoint, data: requestData);
  }

  @override
  Future<List<TeamInvitation>> getPendingInvitations() async {
    // This method is now handled by TeamInvitationsRemoteDataSource
    throw UnimplementedError(
      'Use TeamInvitationsRemoteDataSource for invitation operations',
    );
  }

  @override
  Future<String> uploadTeamLogo(dynamic imageFile) async {
    // Create FormData for file upload
    MultipartFile multipartFile;

    if (imageFile is File) {
      multipartFile = await MultipartFile.fromFile(
        imageFile.path,
        filename: 'team_logo_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
    } else {
      // For test cases or mock files
      multipartFile = MultipartFile.fromBytes(
        [0], // dummy bytes
        filename: 'team_logo_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
    }

    final formData = FormData.fromMap({
      'file': multipartFile,
      'type': 'team_logo',
    });

    final response = await apiClient.post(
      ApiConst.fileUploadEndpoint,
      data: formData,
    );

    return response['url'] ?? response['file_url'] ?? '';
  }

  @override
  Future<List<Team>> getTeamsLeaderboard({int? limit}) async {
    String endpoint = ApiConst.teamLeaderboardEndpoint;
    if (limit != null) {
      endpoint += '?limit=$limit';
    }
    final response = await apiClient.get(endpoint);
    final List<dynamic> data = response['data'] ?? response;
    return data.map((json) {
      final dto = TeamDto.fromJson(json);
      return _mapDtoToEntity(dto);
    }).toList();
  }

  @override
  Future<List<Team>> searchTeams({
    required String query,
    int? limit,
    int? offset,
  }) async {
    String endpoint = '${ApiConst.teamSearchEndpoint}?q=$query';
    if (limit != null) endpoint += '&limit=$limit';
    if (offset != null) endpoint += '&offset=$offset';

    final response = await apiClient.get(endpoint);
    final List<dynamic> data = response['data'] ?? response;
    return data.map((json) {
      final dto = TeamDto.fromJson(json);
      return _mapDtoToEntity(dto);
    }).toList();
  }

  @override
  Future<List<Team>> getAllTeams({int? limit, int? offset}) async {
    String endpoint = ApiConst.teamsEndpoint;
    final params = <String>[];
    if (limit != null) params.add('limit=$limit');
    if (offset != null) params.add('offset=$offset');
    if (params.isNotEmpty) endpoint += '?${params.join('&')}';

    final response = await apiClient.get(endpoint);
    final List<dynamic> data = response['data'] ?? response;
    return data.map((json) {
      final dto = TeamDto.fromJson(json);
      return _mapDtoToEntity(dto);
    }).toList();
  }

  // Helper methods to map DTOs to entities
  Team _mapDtoToEntity(TeamDto dto) {
    return Team(
      id: dto.id,
      teamCode: dto.teamCode,
      name: dto.name,
      description: dto.description,
      slogan: dto.slogan,
      logoUrl: dto.logoUrl,
      isActive: dto.isActive,
      members:
          dto.members
              .map((memberDto) => _mapMemberDtoToEntity(memberDto))
              .toList(),
      created: DateTime.parse(dto.created),
      stats: _mapStatsDtoToEntity(dto.stats),
    );
  }

  TeamMember _mapMemberDtoToEntity(TeamMemberDto dto) {
    return TeamMember(
      id: dto.id,
      userId: dto.userId,
      userName: dto.userName,
      playerId: dto.playerId,
      playerName: dto.playerName,
      role: dto.role,
      joinedAt: DateTime.parse(dto.joinedAt),
      isActive: dto.isActive,
    );
  }

  @override
  Future<PaginatedResponse<TeamSearchItem>> searchTeamsPaginated({
    String? query,
    int? page,
    int? pageSize,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        if (query != null && query.isNotEmpty) 'search': query,
        if (page != null) 'page': page,
        if (pageSize != null) 'pageSize': pageSize,
      };

      final response = await apiClient.get(
        ApiConst.searchTeamsEndpoint,
        query: queryParams,
      );

      return PaginatedResponse<TeamSearchItem>.fromJson(
        response,
        (json) => TeamSearchItemDto.fromJson(json).toEntity(),
      );
    } catch (e) {
      if (e is DioExceptionHandle) {
        rethrow;
      }
      if (e is DioException) {
        throw DioExceptionHandle.fromDioError(e);
      }
      throw Exception('Failed to search teams: ${e.toString()}');
    }
  }

  @override
  Future<PaginatedResponse<TeamSearchItem>> getAllTeamsPaginated({
    int? page,
    int? pageSize,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        if (page != null) 'page': page,
        if (pageSize != null) 'pageSize': pageSize,
      };

      final response = await apiClient.get(
        ApiConst.allTeamsEndpoint,
        query: queryParams,
      );

      return PaginatedResponse<TeamSearchItem>.fromJson(
        response,
        (json) => TeamSearchItemDto.fromJson(json).toEntity(),
      );
    } catch (e) {
      if (e is DioExceptionHandle) {
        rethrow;
      }
      if (e is DioException) {
        throw DioExceptionHandle.fromDioError(e);
      }
      throw Exception('Failed to get teams: ${e.toString()}');
    }
  }

  TeamStats _mapStatsDtoToEntity(TeamStatsDto dto) {
    return TeamStats(
      wins: dto.wins,
      losses: dto.losses,
      draws: dto.draws,
      scoredGoals: dto.scoredGoals,
      concededGoals: dto.concededGoals,
    );
  }

  /// Maps TeamDto to TeamSearchItem for my teams list
  TeamSearchItem _mapTeamDtoToSearchItem(TeamDto dto) {
    return TeamSearchItem(
      id: dto.id,
      name: dto.name,
      membersCount: dto.members.length,
      winRate: _calculateWinRate(dto.stats).toStringAsFixed(2),
      logoUrl: dto.logoUrl,
    );
  }

  double _calculateWinRate(TeamStatsDto stats) {
    final totalMatches = stats.wins + stats.losses + stats.draws;
    if (totalMatches == 0) return 0.0;
    return stats.wins / totalMatches;
  }
}
