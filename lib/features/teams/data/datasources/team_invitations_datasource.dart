import '../../domain/entities/team.dart';

/// Abstract interface for team invitations data operations
abstract interface class TeamInvitationsDatasource {
  /// Get all pending invitations for the current user
  Future<List<TeamInvitation>> getPendingInvitations();

  /// Get invitations for a specific team
  Future<List<TeamInvitation>> getTeamInvitations(String teamId);

  /// Get a specific invitation by ID
  Future<TeamInvitation> getInvitation(String invitationId);

  /// Invite a player to a team
  Future<void> invitePlayer({
    required String teamId,
    required String playerId,
    String role = 'Player',
    String? message,
  });

  /// Accept an invitation
  Future<void> acceptInvitation(String invitationId);

  /// Decline an invitation
  Future<void> declineInvitation(String invitationId);

  /// Get pending invitations count for a team
  Future<int> getTeamInvitationsCount(String teamId);
}
