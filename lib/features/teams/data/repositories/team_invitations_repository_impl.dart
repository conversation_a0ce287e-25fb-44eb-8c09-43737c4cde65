import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/networking/exception.dart';
import '../../domain/entities/team.dart';
import '../../domain/repositories/team_invitations_repository.dart';
import '../datasources/team_invitations_datasource.dart';

/// Repository implementation for team invitations operations
class TeamInvitationsRepositoryImpl implements TeamInvitationsRepository {
  final TeamInvitationsDatasource _dataSource;

  TeamInvitationsRepositoryImpl(this._dataSource);

  @override
  Future<Either<AppError, List<TeamInvitation>>> getPendingInvitations() async {
    try {
      final invitations = await _dataSource.getPendingInvitations();
      return Right(invitations);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, List<TeamInvitation>>> getTeamInvitations(
    String teamId,
  ) async {
    try {
      final invitations = await _dataSource.getTeamInvitations(teamId);
      return Right(invitations);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, TeamInvitation>> getInvitation(
    String invitationId,
  ) async {
    try {
      final invitation = await _dataSource.getInvitation(invitationId);
      return Right(invitation);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> invitePlayer({
    required String teamId,
    required String playerId,
    String role = 'Player',
    String? message,
  }) async {
    try {
      await _dataSource.invitePlayer(
        teamId: teamId,
        playerId: playerId,
        message: message,
      );
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> acceptInvitation(String invitationId) async {
    try {
      await _dataSource.acceptInvitation(invitationId);
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, void>> declineInvitation(String invitationId) async {
    try {
      await _dataSource.declineInvitation(invitationId);
      return const Right(null);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }

  @override
  Future<Either<AppError, int>> getTeamInvitationsCount(String teamId) async {
    try {
      final count = await _dataSource.getTeamInvitationsCount(teamId);
      return Right(count);
    } on DioExceptionHandle catch (e) {
      return Left(AppError(e.message));
    } catch (e) {
      return Left(AppError(e.toString()));
    }
  }
}
