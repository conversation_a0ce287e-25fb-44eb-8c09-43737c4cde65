class TeamInvitationDto {
  final String id;
  final String teamId;
  final String invitedUserId;
  final String invitedUserName;
  final String? invitedUserEmail;
  final String? invitedUserPhone;
  final String status;
  final String createdAt;
  final String? respondedAt;

  const TeamInvitationDto({
    required this.id,
    required this.teamId,
    required this.invitedUserId,
    required this.invitedUserName,
    this.invitedUserEmail,
    this.invitedUserPhone,
    required this.status,
    required this.createdAt,
    this.respondedAt,
  });

  factory TeamInvitationDto.fromJson(Map<String, dynamic> json) {
    return TeamInvitationDto(
      id: json['id'] as String,
      teamId: json['team_id'] as String,
      invitedUserId: json['invited_user_id'] as String,
      invitedUserName: json['invited_user_name'] as String,
      invitedUserEmail: json['invited_user_email'] as String?,
      invitedUserPhone: json['invited_user_phone'] as String?,
      status: json['status'] as String,
      createdAt: json['created_at'] as String,
      respondedAt: json['responded_at'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'team_id': teamId,
      'invited_user_id': invitedUserId,
      'invited_user_name': invitedUserName,
      'invited_user_email': invitedUserEmail,
      'invited_user_phone': invitedUserPhone,
      'status': status,
      'created_at': createdAt,
      'responded_at': respondedAt,
    };
  }
}

class InvitationResponseRequestDto {
  final String action; // 'accept' or 'decline'

  const InvitationResponseRequestDto({required this.action});

  Map<String, dynamic> toJson() {
    return {'action': action};
  }
}
