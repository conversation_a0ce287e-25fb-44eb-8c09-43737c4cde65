import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../entities/team.dart';

/// Repository interface for team invitations operations
abstract interface class TeamInvitationsRepository {
  /// Get all pending invitations for the current user
  Future<Either<AppError, List<TeamInvitation>>> getPendingInvitations();

  /// Get invitations for a specific team
  Future<Either<AppError, List<TeamInvitation>>> getTeamInvitations(
    String teamId,
  );

  /// Get a specific invitation by ID
  Future<Either<AppError, TeamInvitation>> getInvitation(String invitationId);

  /// Invite a player to a team
  Future<Either<AppError, void>> invitePlayer({
    required String teamId,
    required String playerId,
    String role = 'Player',
    String? message,
  });

  /// Accept an invitation
  Future<Either<AppError, void>> acceptInvitation(String invitationId);

  /// Decline an invitation
  Future<Either<AppError, void>> declineInvitation(String invitationId);

  /// Get pending invitations count for a team
  Future<Either<AppError, int>> getTeamInvitationsCount(String teamId);
}
