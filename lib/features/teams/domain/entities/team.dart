class Team {
  final String id;
  final String teamCode;
  final String name;
  final String description;
  final String? slogan;
  final String logoUrl;
  final bool isActive;
  final List<TeamMember> members;
  final DateTime created;
  final TeamStats stats;

  const Team({
    required this.id,
    required this.teamCode,
    required this.name,
    required this.description,
    this.slogan,
    required this.logoUrl,
    required this.isActive,
    required this.members,
    required this.created,
    required this.stats,
  });

  factory Team.fromJson(Map<String, dynamic> json) {
    return Team(
      id: json['id'] as String,
      teamCode: json['teamCode'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      slogan: json['slogan'] as String?,
      logoUrl: json['logoUrl'] as String? ?? '',
      isActive: json['isActive'] ?? true,
      members:
          (json['members'] as List<dynamic>?)
              ?.map((memberJson) => TeamMember.fromJson(memberJson))
              .toList() ??
          [],
      created: DateTime.parse(json['created'] as String),
      stats: TeamStats.fromJson(json['stats']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'teamCode': teamCode,
      'name': name,
      'description': description,
      'slogan': slogan,
      'logoUrl': logoUrl,
      'isActive': isActive,
      'members': members.map((member) => member.toJson()).toList(),
      'created': created.toIso8601String(),
      'stats': stats.toJson(),
    };
  }

  Team copyWith({
    String? id,
    String? teamCode,
    String? name,
    String? description,
    String? slogan,
    String? logoUrl,
    bool? isActive,
    List<TeamMember>? members,
    DateTime? created,
    TeamStats? stats,
  }) {
    return Team(
      id: id ?? this.id,
      teamCode: teamCode ?? this.teamCode,
      name: name ?? this.name,
      description: description ?? this.description,
      slogan: slogan ?? this.slogan,
      logoUrl: logoUrl ?? this.logoUrl,
      isActive: isActive ?? this.isActive,
      members: members ?? this.members,
      created: created ?? this.created,
      stats: stats ?? this.stats,
    );
  }
}

class TeamMember {
  final String id;
  final String userId;
  final String userName;
  final String playerId;
  final String playerName;
  final String role;
  final DateTime joinedAt;
  final bool isActive;

  const TeamMember({
    required this.id,
    required this.userId,
    required this.userName,
    required this.playerId,
    required this.playerName,
    required this.role,
    required this.joinedAt,
    required this.isActive,
  });

  factory TeamMember.fromJson(Map<String, dynamic> json) {
    return TeamMember(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      playerId: json['playerId'] as String,
      playerName: json['playerName'] as String,
      role: json['role'] as String,
      joinedAt: DateTime.parse(json['joinedAt'] as String),
      isActive: json['isActive'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'playerId': playerId,
      'playerName': playerName,
      'role': role,
      'joinedAt': joinedAt.toIso8601String(),
      'isActive': isActive,
    };
  }
}

class TeamInvitation {
  final String id;
  final String teamId;
  final String invitedUserId;
  final String invitedUserName;
  final String? invitedUserEmail;
  final String? invitedUserPhone;
  final String status; // 'pending', 'accepted', 'declined'
  final DateTime createdAt;
  final DateTime? respondedAt;

  const TeamInvitation({
    required this.id,
    required this.teamId,
    required this.invitedUserId,
    required this.invitedUserName,
    this.invitedUserEmail,
    this.invitedUserPhone,
    required this.status,
    required this.createdAt,
    this.respondedAt,
  });

  factory TeamInvitation.fromJson(Map<String, dynamic> json) {
    return TeamInvitation(
      id: json['id'] as String,
      teamId: json['team_id'] as String,
      invitedUserId: json['invited_user_id'] as String,
      invitedUserName: json['invited_user_name'] as String,
      invitedUserEmail: json['invited_user_email'] as String?,
      invitedUserPhone: json['invited_user_phone'] as String?,
      status: json['status'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      respondedAt:
          json['responded_at'] != null
              ? DateTime.parse(json['responded_at'] as String)
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'team_id': teamId,
      'invited_user_id': invitedUserId,
      'invited_user_name': invitedUserName,
      'invited_user_email': invitedUserEmail,
      'invited_user_phone': invitedUserPhone,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'responded_at': respondedAt?.toIso8601String(),
    };
  }
}

class TeamStats {
  final int wins;
  final int losses;
  final int draws;
  final int scoredGoals;
  final int concededGoals;

  const TeamStats({
    required this.wins,
    required this.losses,
    required this.draws,
    required this.scoredGoals,
    required this.concededGoals,
  });

  factory TeamStats.fromJson(Map<String, dynamic> json) {
    return TeamStats(
      wins: json['wins'] as int,
      losses: json['losses'] as int,
      draws: json['draws'] as int,
      scoredGoals: json['scoredGoals'] as int,
      concededGoals: json['concededGoals'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wins': wins,
      'losses': losses,
      'draws': draws,
      'scoredGoals': scoredGoals,
      'concededGoals': concededGoals,
    };
  }

  /// Get total matches played
  int get totalMatches => wins + losses + draws;

  /// Get win rate as a percentage (0.0 to 1.0)
  double get winRate {
    if (totalMatches == 0) return 0.0;
    return wins / totalMatches;
  }

  /// Get win rate as a formatted percentage string
  String get winRatePercentage {
    return '${(winRate * 100).toInt()}%';
  }

  /// Get goal difference
  int get goalDifference => scoredGoals - concededGoals;
}

/// Lightweight team entity for search results and public team lists
class TeamSearchItem {
  final String id;
  final String name;
  final int membersCount;
  final String winRate;
  final String logoUrl;

  const TeamSearchItem({
    required this.id,
    required this.name,
    required this.membersCount,
    required this.winRate,
    required this.logoUrl,
  });

  factory TeamSearchItem.fromJson(Map<String, dynamic> json) {
    return TeamSearchItem(
      id: json['id'] as String,
      name: json['name'] as String,
      membersCount: json['membersCount'] as int? ?? 0,
      winRate: json['winRate'] as String? ?? '0.00',
      logoUrl: json['logoUrl'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'membersCount': membersCount,
      'winRate': winRate,
      'logoUrl': logoUrl,
    };
  }

  TeamSearchItem copyWith({
    String? id,
    String? name,
    int? membersCount,
    String? winRate,
    String? logoUrl,
  }) {
    return TeamSearchItem(
      id: id ?? this.id,
      name: name ?? this.name,
      membersCount: membersCount ?? this.membersCount,
      winRate: winRate ?? this.winRate,
      logoUrl: logoUrl ?? this.logoUrl,
    );
  }

  /// Convert winRate string to double for calculations
  double get winRateAsDouble {
    return double.tryParse(winRate) ?? 0.0;
  }

  /// Get formatted win rate percentage
  String get winRatePercentage {
    final rate = winRateAsDouble;
    return '${(rate * 100).toInt()}%';
  }
}

class PlayerStats {
  final int matchesPlayed;
  final int goals;
  final int assists;
  final int cleanSheets;
  final double rating;
  final int yellowCards;
  final int redCards;
  final int minutesPlayed;

  const PlayerStats({
    required this.matchesPlayed,
    required this.goals,
    required this.assists,
    required this.cleanSheets,
    required this.rating,
    required this.yellowCards,
    required this.redCards,
    required this.minutesPlayed,
  });

  factory PlayerStats.fromJson(Map<String, dynamic> json) {
    return PlayerStats(
      matchesPlayed: json['matches_played'] as int,
      goals: json['goals'] as int,
      assists: json['assists'] as int,
      cleanSheets: json['clean_sheets'] as int,
      rating: (json['rating'] as num).toDouble(),
      yellowCards: json['yellow_cards'] as int,
      redCards: json['red_cards'] as int,
      minutesPlayed: json['minutes_played'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'matches_played': matchesPlayed,
      'goals': goals,
      'assists': assists,
      'clean_sheets': cleanSheets,
      'rating': rating,
      'yellow_cards': yellowCards,
      'red_cards': redCards,
      'minutes_played': minutesPlayed,
    };
  }
}
