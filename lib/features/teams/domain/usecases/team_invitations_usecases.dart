import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../../../../core/use_case.dart';
import '../entities/team.dart';
import '../repositories/team_invitations_repository.dart';

/// Use case for getting pending invitations
class GetPendingInvitationsUseCase implements UseCase<List<TeamInvitation>, NoParams> {
  final TeamInvitationsRepository repository;

  GetPendingInvitationsUseCase(this.repository);

  @override
  Future<Either<AppError, List<TeamInvitation>>> call(NoParams params) async {
    return await repository.getPendingInvitations();
  }
}

/// Use case for getting team invitations
class GetTeamInvitationsUseCase implements UseCase<List<TeamInvitation>, String> {
  final TeamInvitationsRepository repository;

  GetTeamInvitationsUseCase(this.repository);

  @override
  Future<Either<AppError, List<TeamInvitation>>> call(String teamId) async {
    return await repository.getTeamInvitations(teamId);
  }
}

/// Use case for getting a specific invitation
class GetInvitationUseCase implements UseCase<TeamInvitation, String> {
  final TeamInvitationsRepository repository;

  GetInvitationUseCase(this.repository);

  @override
  Future<Either<AppError, TeamInvitation>> call(String invitationId) async {
    return await repository.getInvitation(invitationId);
  }
}

/// Use case for inviting a player to a team
class InvitePlayerUseCase implements UseCase<void, InvitePlayerParams> {
  final TeamInvitationsRepository repository;

  InvitePlayerUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(InvitePlayerParams params) async {
    return await repository.invitePlayer(
      teamId: params.teamId,
      playerId: params.playerId,
      message: params.message,
    );
  }
}

/// Use case for accepting an invitation
class AcceptInvitationUseCase implements UseCase<void, String> {
  final TeamInvitationsRepository repository;

  AcceptInvitationUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(String invitationId) async {
    return await repository.acceptInvitation(invitationId);
  }
}

/// Use case for declining an invitation
class DeclineInvitationUseCase implements UseCase<void, String> {
  final TeamInvitationsRepository repository;

  DeclineInvitationUseCase(this.repository);

  @override
  Future<Either<AppError, void>> call(String invitationId) async {
    return await repository.declineInvitation(invitationId);
  }
}

/// Use case for getting team invitations count
class GetTeamInvitationsCountUseCase implements UseCase<int, String> {
  final TeamInvitationsRepository repository;

  GetTeamInvitationsCountUseCase(this.repository);

  @override
  Future<Either<AppError, int>> call(String teamId) async {
    return await repository.getTeamInvitationsCount(teamId);
  }
}

// Parameter classes
class InvitePlayerParams {
  final String teamId;
  final String playerId;
  final String? message;

  const InvitePlayerParams({
    required this.teamId,
    required this.playerId,
    this.message,
  });
}
