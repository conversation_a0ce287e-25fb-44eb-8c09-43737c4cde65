import 'package:fpdart/fpdart.dart';
import '../../../../core/networking/app_error.dart';
import '../entities/team.dart';
import '../repositories/teams_repository.dart';

class GetMyTeamsUseCase {
  final TeamsRepository repository;

  GetMyTeamsUseCase(this.repository);

  Future<Either<AppError, List<TeamSearchItem>>> call() async {
    return await repository.getMyTeams();
  }
}

class GetTeamByIdUseCase {
  final TeamsRepository repository;

  GetTeamByIdUseCase(this.repository);

  Future<Either<AppError, Team>> call(String teamId) async {
    return await repository.getTeamById(teamId);
  }
}

class CreateTeamUseCase {
  final TeamsRepository repository;

  CreateTeamUseCase(this.repository);

  Future<Either<AppError, Team>> call({
    required String name,
    required String description,
    String? logo,
    String? slogan,
  }) async {
    return await repository.createTeam(
      name: name,
      description: description,
      logo: logo,
      slogan: slogan,
    );
  }
}

class UpdateTeamUseCase {
  final TeamsRepository repository;

  UpdateTeamUseCase(this.repository);

  Future<Either<AppError, Team>> call({
    required String teamId,
    String? name,
    String? description,
    String? logo,
    String? slogan,
  }) async {
    return await repository.updateTeam(
      teamId: teamId,
      name: name,
      description: description,
      logo: logo,
      slogan: slogan,
    );
  }
}

class DeleteTeamUseCase {
  final TeamsRepository repository;

  DeleteTeamUseCase(this.repository);

  Future<Either<AppError, void>> call(String teamId) async {
    return await repository.deleteTeam(teamId);
  }
}

class InvitePlayerUseCase {
  final TeamsRepository repository;

  InvitePlayerUseCase(this.repository);

  Future<Either<AppError, void>> call({
    required String teamId,
    required String playerId,
    String? message,
  }) async {
    return await repository.invitePlayer(
      teamId: teamId,
      playerId: playerId,
      message: message,
    );
  }
}

class AcceptInvitationUseCase {
  final TeamsRepository repository;

  AcceptInvitationUseCase(this.repository);

  Future<Either<AppError, void>> call(String invitationId) async {
    return await repository.acceptInvitation(invitationId);
  }
}

class DeclineInvitationUseCase {
  final TeamsRepository repository;

  DeclineInvitationUseCase(this.repository);

  Future<Either<AppError, void>> call(String invitationId) async {
    return await repository.declineInvitation(invitationId);
  }
}

class RemoveMemberUseCase {
  final TeamsRepository repository;

  RemoveMemberUseCase(this.repository);

  Future<Either<AppError, void>> call({
    required String teamId,
    required String memberId,
  }) async {
    return await repository.removeMember(teamId: teamId, memberId: memberId);
  }
}

class UpdateMemberRoleUseCase {
  final TeamsRepository repository;

  UpdateMemberRoleUseCase(this.repository);

  Future<Either<AppError, void>> call({
    required String teamId,
    required String memberId,
    required String role,
  }) async {
    return await repository.updateMemberRole(
      teamId: teamId,
      memberId: memberId,
      role: role,
    );
  }
}

class GetPendingInvitationsUseCase {
  final TeamsRepository repository;

  GetPendingInvitationsUseCase(this.repository);

  Future<Either<AppError, List<TeamInvitation>>> call() async {
    return await repository.getPendingInvitations();
  }
}

class GetTeamsLeaderboardUseCase {
  final TeamsRepository repository;

  GetTeamsLeaderboardUseCase(this.repository);

  Future<Either<AppError, List<Team>>> call({int? limit}) async {
    return await repository.getTeamsLeaderboard(limit: limit);
  }
}

class SearchTeamsUseCase {
  final TeamsRepository repository;

  SearchTeamsUseCase(this.repository);

  Future<Either<AppError, List<Team>>> call({
    required String query,
    int? limit,
    int? offset,
  }) async {
    return await repository.searchTeams(
      query: query,
      limit: limit,
      offset: offset,
    );
  }
}

class GetAllTeamsUseCase {
  final TeamsRepository repository;

  GetAllTeamsUseCase(this.repository);

  Future<Either<AppError, List<Team>>> call({int? limit, int? offset}) async {
    return await repository.getAllTeams(limit: limit, offset: offset);
  }
}
